#include <stdio.h>

// 直接插入排序
void InsertionSort(int arr[], int n) {
    printf("\n直接插入排序过程:\n");
    for (int i = 1; i < n; i++) {
        int key = arr[i];
        int j = i - 1;
        while (j >= 0 && arr[j] > key) {
            arr[j + 1] = arr[j];
            j--;
        }
        arr[j + 1] = key;

        printf("第 %d 步: ", i);
        for (int k = 0; k < n; k++) {
            printf("%d ", arr[k]);
        }
        printf("\n");
    }
}

// 希尔排序
void ShellSort(int arr[], int n) {
    printf("\n希尔排序过程:\n");
    for (int gap = n / 2; gap > 0; gap /= 2) {
        for (int i = gap; i < n; i++) {
            int key = arr[i];
            int j = i;
            while (j >= gap && arr[j - gap] > key) {
                arr[j] = arr[j - gap];
                j -= gap;
            }
            arr[j] = key;
        }

        printf("gap = %d: ", gap);
        for (int k = 0; k < n; k++) {
            printf("%d ", arr[k]);
        }
        printf("\n");
    }
}

// 快速排序
void QuickSort(int arr[], int low, int high) {
    if (low < high) {
        int pivot = arr[high];
        int i = low - 1;
        for (int j = low; j < high; j++) {
            if (arr[j] < pivot) {
                i++;
                int temp = arr[i];
                arr[i] = arr[j];
                arr[j] = temp;
            }
        }
        int temp = arr[i + 1];
        arr[i + 1] = arr[high];
        arr[high] = temp;

        printf("Pivot = %d: ", arr[i + 1]);
        for (int k = low; k <= high; k++) {
            printf("%d ", arr[k]);
        }
        printf("\n");

        QuickSort(arr, low, i);
        QuickSort(arr, i + 2, high);
    }
}

// 堆排序
void Heapify(int arr[], int n, int i) {
    int largest = i;
    int left = 2 * i + 1;
    int right = 2 * i + 2;

    if (left < n && arr[left] > arr[largest]) {
        largest = left;
    }
    if (right < n && arr[right] > arr[largest]) {
        largest = right;
    }
    if (largest != i) {
        int temp = arr[i];
        arr[i] = arr[largest];
        arr[largest] = temp;
        Heapify(arr, n, largest);
    }
}

void HeapSort(int arr[], int n) {
    printf("\n堆排序过程:\n");
    for (int i = n / 2 - 1; i >= 0; i--) {
        Heapify(arr, n, i);
    }

    for (int i = n - 1; i > 0; i--) {
        int temp = arr[0];
        arr[0] = arr[i];
        arr[i] = temp;
        Heapify(arr, i, 0);

        printf("第 %d 步: ", n - i);
        for (int k = 0; k < n; k++) {
            printf("%d ", arr[k]);
        }
        printf("\n");
    }
}

int main() {
    // 测试数组
    int arr1[] = {12, 11, 13, 5, 6};
    int n1 = sizeof(arr1) / sizeof(arr1[0]);

    int arr2[] = {12, 11, 13, 5, 6};
    int n2 = sizeof(arr2) / sizeof(arr2[0]);

    int arr3[] = {12, 11, 13, 5, 6};
    int n3 = sizeof(arr3) / sizeof(arr3[0]);

    int arr4[] = {12, 11, 13, 5, 6};
    int n4 = sizeof(arr4) / sizeof(arr4[0]);

    // 直接插入排序
    InsertionSort(arr1, n1);

    // 希尔排序
    ShellSort(arr2, n2);

    // 快速排序
    printf("\n快速排序过程:\n");
    QuickSort(arr3, 0, n3 - 1);
    printf("\n");

    // 堆排序
    HeapSort(arr4, n4);

    return 0;
}
