#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_CHAR 8  // 字符总数

// 哈夫曼树的节点结构
typedef struct Node {
    char character;  // 字符
    int frequency;   // 频度
    char *code;      // 哈夫曼编码
    struct Node *left, *right;  // 左右子节点
} Node;

// 最小堆结构
typedef struct {
    Node *array[MAX_CHAR];
    int size;
} MinHeap;

// 创建一个新的哈夫曼树节点
Node* createNode(char character, int frequency) {
    Node* node = (Node*)malloc(sizeof(Node));  // 分配内存
    if (node == NULL) {
        printf("Memory allocation failed.\n");
        exit(1);
    }
    node->character = character;
    node->frequency = frequency;
    node->code = NULL;  // 初始化编码为空
    node->left = node->right = NULL;
    return node;
}

// 创建最小堆
MinHeap* createMinHeap() {
    MinHeap* heap = (MinHeap*)malloc(sizeof(MinHeap));  // 分配内存
    if (heap == NULL) {
        printf("Memory allocation failed.\n");
        exit(1);
    }
    heap->size = 0;
    return heap;
}

// 最小堆插入节点
void insertMinHeap(MinHeap* heap, Node* node) {
    heap->array[heap->size++] = node;
    int i = heap->size - 1;
    
    // 上浮操作，保持堆性质
    while (i > 0 && heap->array[i]->frequency < heap->array[(i - 1) / 2]->frequency) {
        Node* temp = heap->array[i];
        heap->array[i] = heap->array[(i - 1) / 2];
        heap->array[(i - 1) / 2] = temp;
        i = (i - 1) / 2;
    }
}

// 删除最小堆的根节点
Node* deleteMinHeap(MinHeap* heap) {
    if (heap->size == 0)
        return NULL;

    Node* root = heap->array[0];
    heap->array[0] = heap->array[--heap->size];

    // 下沉操作，保持堆性质
    int i = 0;
    while (2 * i + 1 < heap->size) {
        int smallest = i;
        if (heap->array[2 * i + 1]->frequency < heap->array[smallest]->frequency)
            smallest = 2 * i + 1;
        if (2 * i + 2 < heap->size && heap->array[2 * i + 2]->frequency < heap->array[smallest]->frequency)
            smallest = 2 * i + 2;

        if (smallest != i) {
            Node* temp = heap->array[i];
            heap->array[i] = heap->array[smallest];
            heap->array[smallest] = temp;
            i = smallest;
        } else {
            break;
        }
    }
    return root;
}

// 构建哈夫曼树
Node* buildHuffmanTree(char* characters, int* frequencies, int n) {
    MinHeap* heap = createMinHeap();

    // 创建叶子节点并插入最小堆
    for (int i = 0; i < n; i++) {
        insertMinHeap(heap, createNode(characters[i], frequencies[i]));
    }

    // 构建哈夫曼树
    while (heap->size > 1) {
        // 从最小堆中取出最小的两个节点
        Node* left = deleteMinHeap(heap);
        Node* right = deleteMinHeap(heap);

        // 创建新节点，频度为左右子树频度之和
        Node* newNode = createNode('\0', left->frequency + right->frequency);
        newNode->left = left;
        newNode->right = right;

        // 插入新节点回最小堆
        insertMinHeap(heap, newNode);
    }

    // 返回最终的根节点
    return deleteMinHeap(heap);
}

// 递归生成哈夫曼编码
void generateHuffmanCodes(Node* root, char* code, int index) {
    if (root == NULL)
        return;

    // 如果是叶子节点，保存编码
    if (root->left == NULL && root->right == NULL) {
        root->code = (char*)malloc((index + 1) * sizeof(char));  // 分配内存
        if (root->code == NULL) {
            printf("Memory allocation failed.\n");
            exit(1);
        }

        // 使用 strncpy_s 替代 strncpy
        strncpy_s(root->code, index + 1, code, index);
        root->code[index] = '\0';

        // 输出字符和对应的哈夫曼编码
        printf("%c: %s\n", root->character, root->code);
    }

    // 左子树为0
    code[index] = '0';
    generateHuffmanCodes(root->left, code, index + 1);

    // 右子树为1
    code[index] = '1';
    generateHuffmanCodes(root->right, code, index + 1);
}

int main() {
    char characters[] = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'};
    int frequencies[] = {9, 5, 3, 7, 6, 2, 1, 1};

    // 构建哈夫曼树
    Node* root = buildHuffmanTree(characters, frequencies, MAX_CHAR);

    // 用于存储当前编码
    char code[MAX_CHAR];
    generateHuffmanCodes(root, code, 0);

    return 0;
}
