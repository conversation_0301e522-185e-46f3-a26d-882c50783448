#include <stdio.h>
#include <stdlib.h>

typedef char element_type;  // ����˳����Ԫ������Ϊchar

// ˳���ṹ��
typedef struct SqList {
    element_type* data;  // ˳�������ݴ洢
    int capacity;        // ˳�����������
    int length;          // ˳���ǰ�ĳ���
} SqList;

// ��ʼ��˳���
void initList(SqList* L, int capacity) {
    L->data = (element_type*)malloc(capacity * sizeof(element_type));  // ��̬����ռ�
    if (L->data == NULL) {
        printf("�ڴ����ʧ�ܣ�\n");
        exit(1);
    }
    L->capacity = capacity;
    L->length = 0;
}

// ����˳���
void destroyList(SqList* L) {
    free(L->data);  // �ͷ����ݴ洢�ռ�
    L->data = NULL;
    L->capacity = 0;
    L->length = 0;
}

// �ж�˳����Ƿ�Ϊ��
int isEmpty(SqList* L) {
    return L->length == 0;
}

// ���˳���
void printList(SqList* L) {
    if (isEmpty(L)) {
        printf("˳���Ϊ�գ�\n");
        return;
    }
    for (int i = 0; i < L->length; i++) {
        printf("%c ", L->data[i]);
    }
    printf("\n");
}

// ��ȡ˳���ĳ���
int getLength(SqList* L) {
    return L->length;
}

// ��ȡ˳���ĵ�n��Ԫ��
element_type getElement(SqList* L, int index) {
    if (index < 0 || index >= L->length) {
        printf("����Խ�磡\n");
        exit(1);
    }
    return L->data[index];
}

// ����Ԫ�ص�λ��
int findElement(SqList* L, element_type e) {
    for (int i = 0; i < L->length; i++) {
        if (L->data[i] == e) {
            return i;  // ����Ԫ�ص�λ��
        }
    }
    return -1;  // ����-1��ʾδ�ҵ�
}

// ��ָ��λ�ò���Ԫ��
void insertElement(SqList* L, int index, element_type e) {
    if (index < 0 || index > L->length) {
        printf("����λ����Ч��\n");
        return;
    }

    if (L->length == L->capacity) {
        printf("˳����������޷����룡\n");
        return;
    }

    for (int i = L->length; i > index; i--) {
        L->data[i] = L->data[i - 1];  // ����Ԫ��
    }

    L->data[index] = e;  // ����Ԫ��
    L->length++;
}

// ɾ��ָ��λ�õ�Ԫ��
void deleteElement(SqList* L, int index) {
    if (index < 0 || index >= L->length) {
        printf("ɾ��λ����Ч��\n");
        return;
    }

    for (int i = index; i < L->length - 1; i++) {
        L->data[i] = L->data[i + 1];  // ǰ��Ԫ��
    }

    L->length--;  // ����˳���ĳ���
}

int main() {
    SqList L;
    initList(&L, 10);  // ��ʼ��˳�������Ϊ10

    // ����Ԫ��
    insertElement(&L, 0, 'a');
    insertElement(&L, 1, 'b');
    insertElement(&L, 2, 'c');
    insertElement(&L, 3, 'd');
    insertElement(&L, 4, 'e');

    // ���˳���
    printf("˳���L: ");
    printList(&L);

    // ���˳���ĳ���
    printf("˳���ĳ���: %d\n", getLength(&L));

    // �ж�˳����Ƿ�Ϊ��
    printf("˳����Ƿ�Ϊ��: %s\n", isEmpty(&L) ? "��" : "��");

    // ���˳���ĵ�3��Ԫ��
    printf("˳���ĵ�3��Ԫ��: %c\n", getElement(&L, 2));  // ע�⣺������0��ʼ

    // ���Ԫ��a���߼�λ��
    int index = findElement(&L, 'a');
    if (index != -1) {
        printf("Ԫ��a���߼�λ��: %d\n", index);
    }
    else {
        printf("Ԫ��aδ�ҵ���\n");
    }

    // �ڵ�4��λ���ϲ���Ԫ��f
    insertElement(&L, 3, 'f');
    printf("����Ԫ��f���˳���L: ");
    printList(&L);

    // ɾ����3��Ԫ��
    deleteElement(&L, 2);
    printf("ɾ����3��Ԫ�غ��˳���L: ");
    printList(&L);

    // ����˳���
    destroyList(&L);

    return 0;
}
