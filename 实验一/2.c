#include <stdio.h>
#include <stdlib.h>

typedef char element_type;  // ���嵥�����Ԫ������Ϊchar

// ���嵥����ڵ�ṹ��
typedef struct Node {
    element_type data;  // �ڵ��������
    struct Node* next;  // ָ����һ���ڵ��ָ��
} Node;

// ��ʼ��ѭ��������
void initList(Node** L) {
    *L = NULL;
}

// �ж�ѭ���������Ƿ�Ϊ��
int isEmpty(Node* L) {
    return L == NULL;
}

// ���ѭ��������
void printList(Node* L) {
    if (isEmpty(L)) {
        printf("ѭ��������Ϊ�գ�\n");
        return;
    }
    Node* current = L;
    do {
        printf("%c ", current->data);
        current = current->next;
    } while (current != L);  // ����ֱ���ص�ͷ�ڵ�
    printf("\n");
}

// ��ȡѭ��������ĳ���
int getLength(Node* L) {
    if (isEmpty(L)) {
        return 0;
    }
    int length = 0;
    Node* current = L;
    do {
        length++;
        current = current->next;
    } while (current != L);
    return length;
}

// ��ȡѭ��������ĵ�n��Ԫ�أ���1��ʼ������
element_type getElement(Node* L, int index) {
    if (index < 1 || isEmpty(L)) {
        printf("����Խ�磡\n");
        exit(1);
    }
    Node* current = L;
    int count = 1;
    while (current != L || count == 1) {
        if (count == index) {
            return current->data;
        }
        count++;
        current = current->next;
    }
    return '\0';  // Ĭ�Ϸ���
}

// ����Ԫ�ص�λ�ã�����λ�ô�1��ʼ��
int findElement(Node* L, element_type e) {
    if (isEmpty(L)) {
        return -1;  // ����Ϊ��
    }
    int index = 1;
    Node* current = L;
    do {
        if (current->data == e) {
            return index;
        }
        index++;
        current = current->next;
    } while (current != L);
    return -1;  // û���ҵ�Ԫ��
}

// ��ѭ���������ĩβ����Ԫ��
void insertEnd(Node** L, element_type e) {
    Node* newNode = (Node*)malloc(sizeof(Node));
    if (newNode == NULL) {
        printf("�ڴ����ʧ�ܣ�\n");
        exit(1);
    }
    newNode->data = e;
    if (isEmpty(*L)) {
        *L = newNode;
        newNode->next = newNode;  // ���ڵ�ָ���Լ����γ�ѭ��
    }
    else {
        Node* last = *L;
        while (last->next != *L) {
            last = last->next;
        }
        last->next = newNode;
        newNode->next = *L;  // �½ڵ�ָ��ͷ�ڵ㣬�γ�ѭ��
    }
}

// ��ѭ���������ָ��λ�ò���Ԫ��
void insertAt(Node** L, int index, element_type e) {
    if (index < 1 || index > getLength(*L) + 1) {
        printf("����λ����Ч��\n");
        return;
    }
    if (index == 1) {  // ���뵽ͷ�ڵ�
        Node* newNode = (Node*)malloc(sizeof(Node));
        if (newNode == NULL) {
            printf("�ڴ����ʧ�ܣ�\n");
            exit(1);
        }
        newNode->data = e;
        if (isEmpty(*L)) {
            *L = newNode;
            newNode->next = newNode;  // �½ڵ�ָ���Լ�
        }
        else {
            Node* last = *L;
            while (last->next != *L) {
                last = last->next;
            }
            last->next = newNode;
            newNode->next = *L;
            *L = newNode;  // �½ڵ��Ϊͷ�ڵ�
        }
    }
    else {  // ���뵽ָ��λ��
        Node* newNode = (Node*)malloc(sizeof(Node));
        if (newNode == NULL) {
            printf("�ڴ����ʧ�ܣ�\n");
            exit(1);
        }
        newNode->data = e;
        Node* current = *L;
        for (int i = 1; i < index - 1; i++) {
            current = current->next;
        }
        newNode->next = current->next;
        current->next = newNode;
    }
}

// ɾ��ѭ���������ָ��λ�õ�Ԫ��
void deleteAt(Node** L, int index) {
    if (index < 1 || index > getLength(*L)) {
        printf("ɾ��λ����Ч��\n");
        return;
    }
    Node* current = *L;
    if (index == 1) {  // ɾ��ͷ�ڵ�
        if (current->next == *L) {  // ֻ��һ���ڵ�
            free(current);
            *L = NULL;
        }
        else {
            Node* last = *L;
            while (last->next != *L) {
                last = last->next;
            }
            last->next = current->next;
            *L = current->next;
            free(current);
        }
    }
    else {
        for (int i = 1; i < index - 1; i++) {
            current = current->next;
        }
        Node* temp = current->next;
        current->next = temp->next;
        free(temp);
    }
}

// ����ѭ��������
void destroyList(Node** L) {
    if (isEmpty(*L)) return;
    Node* current = *L;
    Node* temp;
    do {
        temp = current;
        current = current->next;
        free(temp);
    } while (current != *L);
    *L = NULL;
}

int main() {
    Node* L;
    initList(&L);  // ��ʼ��ѭ��������

    // ����Ԫ��
    insertEnd(&L, 'a');
    insertEnd(&L, 'b');
    insertEnd(&L, 'c');
    insertEnd(&L, 'd');
    insertEnd(&L, 'e');

    // ���ѭ��������
    printf("ѭ��������L: ");
    printList(L);

    // ���ѭ��������ĳ���
    printf("ѭ��������ĳ���: %d\n", getLength(L));

    // �ж�ѭ���������Ƿ�Ϊ��
    printf("ѭ���������Ƿ�Ϊ��: %s\n", isEmpty(L) ? "��" : "��");

    // ���ѭ��������ĵ�3��Ԫ��
    printf("ѭ��������ĵ�3��Ԫ��: %c\n", getElement(L, 3));

    // ���Ԫ��'a'���߼�λ��
    int index = findElement(L, 'a');
    if (index != -1) {
        printf("Ԫ��'a'���߼�λ��: %d\n", index);
    }
    else {
        printf("Ԫ��'a'δ�ҵ���\n");
    }

    // �ڵ�4��λ���ϲ���Ԫ��'f'
    insertAt(&L, 4, 'f');
    printf("����Ԫ��'f'���ѭ��������L: ");
    printList(L);

    // ɾ��L�ĵ�3��Ԫ��
    deleteAt(&L, 3);
    printf("ɾ����3��Ԫ�غ��ѭ��������L: ");
    printList(L);

    // ����ѭ��������
    destroyList(&L);

    return 0;
}
