#include <stdio.h>
#include <stdlib.h>
#include <limits.h>

#define MAX_VERTEX 6  // 假设图中最多6个顶点
#define INF INT_MAX   // 定义无限大

// 图 G1，邻接矩阵表示（无向带权图）
int adjMatrixG1[MAX_VERTEX][MAX_VERTEX] = {
    {0, 6, 0, 0, 0, 0},
    {6, 0, 5, 0, 0, 0},
    {0, 5, 0, 4, 0, 0},
    {0, 0, 4, 0, 2, 3},
    {0, 0, 0, 2, 0, 6},
    {0, 0, 0, 3, 6, 0}
};

// 图 G2，邻接矩阵表示（有向带权图）
int adjMatrixG2[MAX_VERTEX][MAX_VERTEX] = {
    {0, 10, 0, 0, 0, 0},
    {0, 0, 5, 0, 0, 0},
    {0, 0, 0, 2, 0, 0},
    {0, 0, 0, 0, 1, 3},
    {0, 0, 0, 0, 0, 8},
    {0, 0, 0, 0, 0, 0}
};

// 普里姆算法 - 最小生成树
void prim(int graph[MAX_VERTEX][MAX_VERTEX]) {
    int selected[MAX_VERTEX] = {0};   // 标记顶点是否已加入生成树
    int edgeCount = 0;                 // 生成树中边的数量
    int totalWeight = 0;               // 最小生成树的权值之和
    selected[0] = 1;                   // 从顶点0开始

    printf("普里姆算法生成的最小生成树的边:\n");
    
    while (edgeCount < MAX_VERTEX - 1) {
        int minWeight = INF;
        int u = -1, v = -1;
        
        // 找到当前生成树外的最小权重边
        for (int i = 0; i < MAX_VERTEX; i++) {
            if (selected[i]) {  // 当前节点在生成树中
                for (int j = 0; j < MAX_VERTEX; j++) {
                    if (!selected[j] && graph[i][j] && graph[i][j] < minWeight) {
                        minWeight = graph[i][j];
                        u = i;
                        v = j;
                    }
                }
            }
        }
        
        selected[v] = 1;  // 加入生成树
        edgeCount++;
        totalWeight += minWeight;
        printf("边: (%d, %d) 权重: %d\n", u, v, minWeight);
    }
    printf("最小生成树的总权值: %d\n", totalWeight);
}

// 克鲁斯卡尔算法 - 最小生成树
typedef struct {
    int u, v, weight;
} Edge;

int find(int parent[], int i) {
    if (parent[i] == i)
        return i;
    return find(parent, parent[i]);
}

void unionSet(int parent[], int rank[], int u, int v) {
    int root_u = find(parent, u);
    int root_v = find(parent, v);
    
    if (root_u != root_v) {
        if (rank[root_u] > rank[root_v]) {
            parent[root_v] = root_u;
        } else if (rank[root_u] < rank[root_v]) {
            parent[root_u] = root_v;
        } else {
            parent[root_v] = root_u;
            rank[root_u]++;
        }
    }
}

void kruskal(int graph[MAX_VERTEX][MAX_VERTEX]) {
    Edge edges[MAX_VERTEX * (MAX_VERTEX - 1) / 2];  // 存储边
    int edgeCount = 0;

    // 将所有边存入 edges 数组
    for (int i = 0; i < MAX_VERTEX; i++) {
        for (int j = i + 1; j < MAX_VERTEX; j++) {
            if (graph[i][j] != 0) {
                edges[edgeCount].u = i;
                edges[edgeCount].v = j;
                edges[edgeCount].weight = graph[i][j];
                edgeCount++;
            }
        }
    }
    
    // 按照边的权重进行排序（简单的冒泡排序）
    for (int i = 0; i < edgeCount - 1; i++) {
        for (int j = 0; j < edgeCount - 1 - i; j++) {
            if (edges[j].weight > edges[j + 1].weight) {
                Edge temp = edges[j];
                edges[j] = edges[j + 1];
                edges[j + 1] = temp;
            }
        }
    }
    
    int parent[MAX_VERTEX], rank[MAX_VERTEX];
    for (int i = 0; i < MAX_VERTEX; i++) {
        parent[i] = i;
        rank[i] = 0;
    }

    printf("克鲁斯卡尔算法生成的最小生成树的边:\n");
    int totalWeight = 0;
    int edgeSelected = 0;

    for (int i = 0; i < edgeCount && edgeSelected < MAX_VERTEX - 1; i++) {
        int u = edges[i].u;
        int v = edges[i].v;
        
        if (find(parent, u) != find(parent, v)) {
            unionSet(parent, rank, u, v);
            edgeSelected++;
            totalWeight += edges[i].weight;
            printf("边: (%d, %d) 权重: %d\n", u, v, edges[i].weight);
        }
    }

    printf("最小生成树的总权值: %d\n", totalWeight);
}

// Dijkstra算法 - 最短路径
void dijkstra(int graph[MAX_VERTEX][MAX_VERTEX], int start) {
    int dist[MAX_VERTEX];     // 存储最短路径的距离
    int visited[MAX_VERTEX];  // 标记顶点是否已访问
    int path[MAX_VERTEX];     // 存储最短路径
    for (int i = 0; i < MAX_VERTEX; i++) {
        dist[i] = INF;
        visited[i] = 0;
        path[i] = -1;
    }
    dist[start] = 0;

    for (int i = 0; i < MAX_VERTEX - 1; i++) {
        int u = -1;
        for (int j = 0; j < MAX_VERTEX; j++) {
            if (!visited[j] && (u == -1 || dist[j] < dist[u])) {
                u = j;
            }
        }

        visited[u] = 1;

        for (int v = 0; v < MAX_VERTEX; v++) {
            if (graph[u][v] && !visited[v] && dist[u] + graph[u][v] < dist[v]) {
                dist[v] = dist[u] + graph[u][v];
                path[v] = u;
            }
        }
    }

    printf("从顶点 %d 到其它顶点的最短路径及距离:\n", start);
    for (int i = 0; i < MAX_VERTEX; i++) {
        if (i != start) {
            printf("顶点 %d 距离: %d\n", i, dist[i]);
        }
    }
}

int main() {
    printf("1. 普里姆算法最小生成树\n");
    prim(adjMatrixG1);
    
    printf("\n2. 克鲁斯卡尔算法最小生成树\n");
    kruskal(adjMatrixG1);
    
    printf("\n3. Dijkstra算法最短路径（从顶点0开始）\n");
    dijkstra(adjMatrixG2, 0);
    
    return 0;
}
