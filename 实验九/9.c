#include <stdio.h>
#include <stdlib.h>

#define TABLE_SIZE 10

// 定义顺序表的元素类型
typedef int element_type;

// 定义散列表的结构体
typedef int KeyType;
typedef int InfoType;

typedef struct {
    KeyType key;  // 关键字
    InfoType data;  // 其他数据
} NodeType;

typedef NodeType* HashTable[TABLE_SIZE];

// 顺序查找算法
void SequentialSearch(element_type arr[], int length, element_type key) {
    int i;
    for (i = 0; i < length; i++) {
        printf("第 %d 步：检查元素 %d\n", i + 1, arr[i]);
        if (arr[i] == key) {
            printf("找到关键字 %d，位置：%d\n", key, i + 1);
            return;
        }
    }
    printf("关键字 %d 未找到\n", key);
}

// 二分查找算法
void BinarySearch(element_type arr[], int left, int right, element_type key) {
    int mid;
    while (left <= right) {
        mid = left + (right - left) / 2;
        printf("检查中间元素：%d\n", arr[mid]);

        if (arr[mid] == key) {
            printf("找到关键字 %d，位置：%d\n", key, mid + 1);
            return;
        }
        else if (arr[mid] < key) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    printf("关键字 %d 未找到\n", key);
}

// 哈希函数（除留余数法）
int Hash(KeyType key) {
    return key % TABLE_SIZE;
}

// 构造散列表
void ConstructHashTable(HashTable table, NodeType elements[], int n) {
    for (int i = 0; i < n; i++) {
        int index = Hash(elements[i].key);
        int originalIndex = index;
        
        // 线性探测法解决冲突
        while (table[index] != NULL) {
            printf("发生冲突，尝试下一个位置\n");
            index = (index + 1) % TABLE_SIZE;
            if (index == originalIndex) {
                printf("散列表已满，无法插入元素！\n");
                return;
            }
        }
        table[index] = &elements[i];
    }
}

// 查找散列表中的关键字
NodeType* SearchHashTable(HashTable table, KeyType key) {
    int index = Hash(key);
    int originalIndex = index;
    
    while (table[index] != NULL) {
        if (table[index]->key == key) {
            return table[index];
        }
        index = (index + 1) % TABLE_SIZE;
        if (index == originalIndex) {
            break;
        }
    }
    return NULL;
}

int main() {
    // (1) 顺序查找
    element_type arr1[] = {3, 6, 2, 10, 1, 8, 5, 7, 4, 9};
    int length1 = sizeof(arr1) / sizeof(arr1[0]);
    int key1 = 5;

    printf("顺序查找过程：\n");
    SequentialSearch(arr1, length1, key1);

    // (2) 二分查找
    element_type arr2[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int length2 = sizeof(arr2) / sizeof(arr2[0]);
    int key2 = 9;

    printf("\n二分查找过程：\n");
    BinarySearch(arr2, 0, length2 - 1, key2);

    // (3) 散列表实现与查找（选做）
    NodeType elements[] = { {15, 100},  {25, 200}, {35, 300}, {45, 400} };
    HashTable table = { NULL };

    // 构造散列表
    ConstructHashTable(table, elements, 4);

    // 查找
    KeyType key3 = 25;
    NodeType* result = SearchHashTable(table, key3);

    if (result != NULL) {
        printf("\n在散列表中找到关键字 %d, 对应数据: %d\n", result->key, result->data);
    } else {
        printf("\n在散列表中未找到关键字 %d\n", key3);
    }

    return 0;
}
