#include <stdio.h>
#include <stdlib.h>

struct DNode {
    int data;
    struct DNode *prev;
    struct DNode *next;
};

void reverseList(struct DNode *head) {
    struct DNode *temp = NULL;
    struct DNode *current = head->next;

    // 交换prev和next指针
    while (current != NULL) {
        temp = current->prev;
        current->prev = current->next;
        current->next = temp;
        current = current->prev;
    }

    // 修正头节点
    if (temp != NULL) {
        head->next = temp->prev;
    }
}

void printList(struct DNode *head) {
    struct DNode *temp = head->next;
    while (temp != NULL) {
        printf("%d ", temp->data);
        temp = temp->next;
    }
    printf("\n");
}

int main() {
    struct DNode *head = (struct DNode *)malloc(sizeof(struct DNode));
    head->prev = NULL;
    head->next = NULL;

    // 假设双链表为 1 <-> 2 <-> 3 <-> 4
    struct DNode *n1 = (struct DNode *)malloc(sizeof(struct DNode));
    n1->data = 1;
    head->next = n1;
    n1->prev = head;

    struct DNode *n2 = (struct DNode *)malloc(sizeof(struct DNode));
    n2->data = 2;
    n1->next = n2;
    n2->prev = n1;

    struct DNode *n3 = (struct DNode *)malloc(sizeof(struct DNode));
    n3->data = 3;
    n2->next = n3;
    n3->prev = n2;

    struct DNode *n4 = (struct DNode *)malloc(sizeof(struct DNode));
    n4->data = 4;
    n3->next = n4;
    n4->prev = n3;
    n4->next = NULL;

    printf("Original list: ");
    printList(head);

    reverseList(head);

    printf("Reversed list: ");
    printList(head);

    return 0;
}
