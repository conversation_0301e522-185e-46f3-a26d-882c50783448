#include <stdio.h>
#include <stdlib.h>

struct Node {
    int data;
    struct Node *next;
};

void partitionList(struct Node *head) {
    if (head == NULL || head->next == NULL) return;

    int x = head->next->data; // 基准值为首结点的值
    struct Node *lessHead = (struct Node *)malloc(sizeof(struct Node));
    struct Node *greaterHead = (struct Node *)malloc(sizeof(struct Node));
    struct Node *less = lessHead, *greater = greaterHead;
    struct Node *curr = head->next->next;

    while (curr != NULL) {
        if (curr->data < x) {
            less->next = curr;
            less = less->next;
        } else {
            greater->next = curr;
            greater = greater->next;
        }
        curr = curr->next;
    }

    greater->next = NULL;
    less->next = greaterHead->next;
    head->next = lessHead->next;

    free(lessHead);
    free(greaterHead);
}

void printList(struct Node *head) {
    struct Node *temp = head->next;
    while (temp != NULL) {
        printf("%d ", temp->data);
        temp = temp->next;
    }
    printf("\n");
}

int main() {
    struct Node *head = (struct Node *)malloc(sizeof(struct Node));
    head->next = NULL;

    // 假设链表为 3 -> 5 -> 4 -> 1 -> 2
    struct Node *n1 = (struct Node *)malloc(sizeof(struct Node));
    n1->data = 3;
    head->next = n1;

    struct Node *n2 = (struct Node *)malloc(sizeof(struct Node));
    n2->data = 5;
    n1
