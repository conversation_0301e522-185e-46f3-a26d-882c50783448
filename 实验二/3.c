#include <stdio.h>
#include <stdlib.h>

struct Node {
    int data;
    struct Node *next;
};

void deleteNodes(struct Node *head, int min, int max) {
    struct Node *prev = head;
    struct Node *curr = head->next;

    while (curr != NULL) {
        if (curr->data > min && curr->data < max) {
            prev->next = curr->next;
            free(curr);
            curr = prev->next;
        } else {
            prev = curr;
            curr = curr->next;
        }
    }
}

void printList(struct Node *head) {
    struct Node *temp = head->next;
    while (temp != NULL) {
        printf("%d ", temp->data);
        temp = temp->next;
    }
    printf("\n");
}

int main() {
    struct Node *head = (struct Node *)malloc(sizeof(struct Node));
    head->next = NULL;

    // 假设已有递增单链表 1 -> 3 -> 5 -> 7 -> 9
    struct Node *n1 = (struct Node *)malloc(sizeof(struct Node));
    n1->data = 1;
    head->next = n1;

    struct Node *n2 = (struct Node *)malloc(sizeof(struct Node));
    n2->data = 3;
    n1->next = n2;

    struct Node *n3 = (struct Node *)malloc(sizeof(struct Node));
    n3->data = 5;
    n2->next = n3;

    struct Node *n4 = (struct Node *)malloc(sizeof(struct Node));
    n4->data = 7;
    n3->next = n4;

    struct Node *n5 = (struct Node *)malloc(sizeof(struct Node));
    n5->data = 9;
    n4->next = n5;
    n5->next = NULL;

    printf("Original list: ");
    printList(head);

    deleteNodes(head, 3, 7);

    printf("List after deletion: ");
    printList(head);

    return 0;
}
