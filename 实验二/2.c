#include <stdio.h>

void intersection(int La[], int Lb[], int m, int n) {
    int i = 0, j = 0;
    while (i < m && j < n) {
        if (La[i] < Lb[j]) {
            i++;
        } else if (La[i] > Lb[j]) {
            j++;
        } else {
            printf("%d ", La[i]);
            i++;
            j++;
        }
    }
}

int main() {
    int La[] = {1, 3, 5, 7, 9};
    int Lb[] = {2, 3, 5, 6, 9};
    int m = 5, n = 5;
    intersection(La, Lb, m, n);
    return 0;
}
