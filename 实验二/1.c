#include <stdio.h>

void mergeSortedSequences(int A[], int m, int n) {
    int i = m - 1;  // 前m个有序部分的最后一个元素
    int j = m;      // 后n个有序部分的第一个元素
    int k = m + n - 1;  // 合并后的顺序表的最后一个元素

    while (i >= 0 && j < m + n) {
        if (A[i] > A[j]) {
            A[k--] = A[i--];
        } else {
            A[k--] = A[j++];
        }
    }

    // 如果有剩余的元素
    while (i >= 0) {
        A[k--] = A[i--];
    }
    while (j < m + n) {
        A[k--] = A[j++];
    }
}

int main() {
    int A[] = {1, 3, 5, 0, 0, 0};
    int m = 3, n = 3;
    mergeSortedSequences(A, m, n);
    for (int i = 0; i < m + n; i++) {
        printf("%d ", A[i]);
    }
    return 0;
}
