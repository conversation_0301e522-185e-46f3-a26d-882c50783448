#include <stdio.h>
#include <stdlib.h>

#define MAX_VERTEX 6  // 图中顶点数量，假设为6个顶点（从0到5）

// 邻接矩阵表示图
int adjMatrix[MAX_VERTEX][MAX_VERTEX] = {
    {0, 1, 0, 0, 0, 0},
    {0, 0, 1, 0, 0, 0},
    {0, 0, 0, 1, 0, 0},
    {0, 0, 0, 0, 1, 0},
    {0, 0, 0, 0, 0, 1},
    {0, 0, 0, 0, 0, 0}
};

// 邻接表表示图
typedef struct Edge {
    int dest;
    int weight;
    struct Edge* next;
} Edge;

typedef struct Vertex {
    int vertex;
    Edge* adjList;
} Vertex;

Vertex graph[MAX_VERTEX];

// 深度优先遍历（DFS）
void DFS(int v, int visited[], int matrix) {
    visited[v] = 1;
    printf("%d ", v);
    if (matrix) {
        for (int i = 0; i < MAX_VERTEX; i++) {
            if (adjMatrix[v][i] && !visited[i]) {
                DFS(i, visited, matrix);
            }
        }
    } else {
        Edge* temp = graph[v].adjList;
        while (temp) {
            if (!visited[temp->dest]) {
                DFS(temp->dest, visited, matrix);
            }
            temp = temp->next;
        }
    }
}

// 广度优先遍历（BFS）
void BFS(int start, int visited[], int matrix) {
    int queue[MAX_VERTEX];
    int front = -1, rear = -1;
    visited[start] = 1;
    queue[++rear] = start;
    
    while (front != rear) {
        int v = queue[++front];
        printf("%d ", v);
        
        if (matrix) {
            for (int i = 0; i < MAX_VERTEX; i++) {
                if (adjMatrix[v][i] && !visited[i]) {
                    visited[i] = 1;
                    queue[++rear] = i;
                }
            }
        } else {
            Edge* temp = graph[v].adjList;
            while (temp) {
                if (!visited[temp->dest]) {
                    visited[temp->dest] = 1;
                    queue[++rear] = temp->dest;
                }
                temp = temp->next;
            }
        }
    }
}

// 计算入度和出度（邻接矩阵实现）
void calculateDegreeMatrix() {
    int inDegree[MAX_VERTEX] = {0};
    int outDegree[MAX_VERTEX] = {0};
    
    for (int i = 0; i < MAX_VERTEX; i++) {
        for (int j = 0; j < MAX_VERTEX; j++) {
            if (adjMatrix[i][j]) {
                outDegree[i]++;
                inDegree[j]++;
            }
        }
    }
    
    printf("\n邻接矩阵的入度和出度:\n");
    for (int i = 0; i < MAX_VERTEX; i++) {
        printf("顶点 %d: 入度 = %d, 出度 = %d\n", i, inDegree[i], outDegree[i]);
    }
}

// 计算入度和出度（邻接表实现）
void calculateDegreeList() {
    int inDegree[MAX_VERTEX] = {0};
    int outDegree[MAX_VERTEX] = {0};
    
    for (int i = 0; i < MAX_VERTEX; i++) {
        Edge* temp = graph[i].adjList;
        while (temp) {
            outDegree[i]++;
            inDegree[temp->dest]++;
            temp = temp->next;
        }
    }
    
    printf("\n邻接表的入度和出度:\n");
    for (int i = 0; i < MAX_VERTEX; i++) {
        printf("顶点 %d: 入度 = %d, 出度 = %d\n", i, inDegree[i], outDegree[i]);
    }
}

// 创建图（邻接表实现）
void createGraph() {
    for (int i = 0; i < MAX_VERTEX; i++) {
        graph[i].vertex = i;
        graph[i].adjList = NULL;
    }
    
    // 添加边
    int edges[6][2] = {
        {0, 1}, {1, 2}, {2, 3}, {3, 4}, {4, 5}, {5, 0}
    };
    
    for (int i = 0; i < 6; i++) {
        int src = edges[i][0];
        int dest = edges[i][1];
        
        // 创建新边
        Edge* newEdge = (Edge*)malloc(sizeof(Edge));
        newEdge->dest = dest;
        newEdge->weight = 1;  // 假设所有边的权重为1
        newEdge->next = graph[src].adjList;
        graph[src].adjList = newEdge;
    }
}

// 选做部分：找顶点5到顶点2的所有简单路径
void findPaths(int start, int end, int visited[], int path[], int pathIndex) {
    visited[start] = 1;
    path[pathIndex] = start;
    pathIndex++;
    
    if (start == end) {
        for (int i = 0; i < pathIndex; i++) {
            printf("%d ", path[i]);
        }
        printf("\n");
    } else {
        Edge* temp = graph[start].adjList;
        while (temp) {
            if (!visited[temp->dest]) {
                findPaths(temp->dest, end, visited, path, pathIndex);
            }
            temp = temp->next;
        }
    }
    visited[start] = 0;
}

int main() {
    // 邻接矩阵计算入度出度
    calculateDegreeMatrix();
    
    // 邻接表计算入度出度
    createGraph();
    calculateDegreeList();
    
    // 深度优先遍历（DFS）邻接矩阵
    printf("\n深度优先遍历（邻接矩阵）: ");
    int visitedMatrix[MAX_VERTEX] = {0};
    DFS(0, visitedMatrix, 1);
    printf("\n");
    
    // 深度优先遍历（DFS）邻接表
    printf("\n深度优先遍历（邻接表）: ");
    int visitedList[MAX_VERTEX] = {0};
    DFS(0, visitedList, 0);
    printf("\n");
    
    // 广度优先遍历（BFS）邻接矩阵
    printf("\n广度优先遍历（邻接矩阵）: ");
    int visitedMatrixBFS[MAX_VERTEX] = {0};
    BFS(0, visitedMatrixBFS, 1);
    printf("\n");
    
    // 广度优先遍历（BFS）邻接表
    printf("\n广度优先遍历（邻接表）: ");
    int visitedListBFS[MAX_VERTEX] = {0};
    BFS(0, visitedListBFS, 0);
    printf("\n");
    
    // 选做部分：查找顶点5到顶点2的所有简单路径
    printf("\n顶点5到顶点2的所有路径:\n");
    int visitedPath[MAX_VERTEX] = {0};
    int path[MAX_VERTEX];
    findPaths(5, 2, visitedPath, path, 0);

    return 0;
}
