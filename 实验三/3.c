#include <stdio.h>
#include <stdlib.h>

// ˳��ջ�Ķ���
#define MAX_STACK_SIZE 10

typedef struct {
    int data[MAX_STACK_SIZE];
    int top;
} Stack;

// ѭ�����еĶ���
#define MAX_QUEUE_SIZE 10

typedef struct {
    int data[MAX_QUEUE_SIZE];
    int front;
    int rear;
} Queue;

// ջ����ʵ��
// ��ʼ��ջ
void initStack(Stack *S) {
    S->top = -1;
}

// �ж�ջ�Ƿ���
int isFullStack(Stack *S) {
    return S->top == MAX_STACK_SIZE - 1;
}

// �ж�ջ�Ƿ��
int isEmptyStack(Stack *S) {
    return S->top == -1;
}

// ��ջ����
void push(Stack *S, int value) {
    if (isFullStack(S)) {
        printf("ջ�������޷���ջ\n");
    } else {
        S->data[++(S->top)] = value;
    }
}

// ��ջ����
int pop(Stack *S) {
    if (isEmptyStack(S)) {
        printf("ջΪ�գ��޷���ջ\n");
        return -1;
    } else {
        return S->data[(S->top)--];
    }
}

// ��ȡջ��Ԫ��
int peekStack(Stack *S) {
    if (isEmptyStack(S)) {
        printf("ջΪ�գ��޷���ȡջ��Ԫ��\n");
        return -1;
    } else {
        return S->data[S->top];
    }
}

// ����ջ
void destroyStack(Stack *S) {
    S->top = -1;
}

// ���в���ʵ��
// ��ʼ������
void initQueue(Queue *Q) {
    Q->front = 0;
    Q->rear = 0;
}

// �ж϶����Ƿ���
int isFullQueue(Queue *Q) {
    return (Q->rear + 1) % MAX_QUEUE_SIZE == Q->front;
}

// �ж϶����Ƿ��
int isEmptyQueue(Queue *Q) {
    return Q->front == Q->rear;
}

// ��Ӳ���
void enqueue(Queue *Q, int value) {
    if (isFullQueue(Q)) {
        printf("�����������޷����\n");
    } else {
        Q->data[Q->rear] = value;
        Q->rear = (Q->rear + 1) % MAX_QUEUE_SIZE;
    }
}

// ���Ӳ���
int dequeue(Queue *Q) {
    if (isEmptyQueue(Q)) {
        printf("����Ϊ�գ��޷�����\n");
        return -1;
    } else {
        int value = Q->data[Q->front];
        Q->front = (Q->front + 1) % MAX_QUEUE_SIZE;
        return value;
    }
}

// ��ȡ��ͷԪ��
int peekQueue(Queue *Q) {
    if (isEmptyQueue(Q)) {
        printf("����Ϊ�գ��޷���ȡ��ͷԪ��\n");
        return -1;
    } else {
        return Q->data[Q->front];
    }
}

// ���ٶ���
void destroyQueue(Queue *Q) {
    Q->front = Q->rear = 0;
}

// ����������
int main() {
    // ����ջ
    Stack S;
    initStack(&S);

    printf("˳��ջ��������:\n");
    push(&S, 1);
    push(&S, 2);
    push(&S, 3);

    printf("ջ�Ƿ���: %s\n", isFullStack(&S) ? "��" : "��");

    printf("��ջ: %d\n", pop(&S));
    printf("��ջ: %d\n", pop(&S));

    printf("ջ�Ƿ��: %s\n", isEmptyStack(&S) ? "��" : "��");

    printf("ջ��Ԫ��: %d\n", peekStack(&S));

    destroyStack(&S);

    // ���Զ���
    Queue Q;
    initQueue(&Q);

    printf("\nѭ�����в�������:\n");
    enqueue(&Q, 1);
    enqueue(&Q, 2);
    enqueue(&Q, 3);

    printf("�����Ƿ���: %s\n", isFullQueue(&Q) ? "��" : "��");

    printf("����: %d\n", dequeue(&Q));
    printf("����: %d\n", dequeue(&Q));

    printf("�����Ƿ��: %s\n", isEmptyQueue(&Q) ? "��" : "��");

    printf("��ͷԪ��: %d\n", peekQueue(&Q));

    destroyQueue(&Q);

    return 0;
}