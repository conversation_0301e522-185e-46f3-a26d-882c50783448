#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>

// 二叉树节点结构定义
typedef struct BiTNode {
    int data;
    struct BiTNode* lchild;
    struct BiTNode* rchild;
} BiTNode, *BiTree;

// 队列节点结构（用于层序遍历）
typedef struct QueueNode {
    BiTree data;
    struct QueueNode* next;
} QueueNode;

// 队列结构
typedef struct {
    QueueNode* front;
    QueueNode* rear;
} Queue;

// ===== 队列操作（用于层序遍历） =====
void InitQueue(Queue* Q) {
    Q->front = Q->rear = NULL;
}

bool QueueEmpty(Queue* Q) {
    return Q->front == NULL;
}

void EnQueue(Queue* Q, BiTree T) {
    QueueNode* newNode = (QueueNode*)malloc(sizeof(QueueNode));
    newNode->data = T;
    newNode->next = NULL;
    
    if (Q->rear == NULL) {
        Q->front = Q->rear = newNode;
    } else {
        Q->rear->next = newNode;
        Q->rear = newNode;
    }
}

BiTree DeQueue(Queue* Q) {
    if (QueueEmpty(Q)) return NULL;
    
    QueueNode* temp = Q->front;
    BiTree data = temp->data;
    Q->front = Q->front->next;
    
    if (Q->front == NULL) {
        Q->rear = NULL;
    }
    
    free(temp);
    return data;
}

// ===== 二叉树基本操作 =====

// 创建节点
BiTree CreateNode(int data) {
    BiTree node = (BiTree)malloc(sizeof(BiTNode));
    node->data = data;
    node->lchild = NULL;
    node->rchild = NULL;
    return node;
}

// 根据图1创建二叉树
BiTree CreateBinaryTree() {
    // 创建节点
    BiTree root = CreateNode(1);
    BiTree node2 = CreateNode(2);
    BiTree node3 = CreateNode(3);
    BiTree node4 = CreateNode(4);
    BiTree node5 = CreateNode(5);
    BiTree node6 = CreateNode(6);
    
    // 建立连接关系
    root->lchild = node2;
    root->rchild = node3;
    node3->lchild = node4;
    node3->rchild = node5;
    node4->lchild = node6;
    
    return root;
}

// （1）求二叉树的高度
int GetHeight(BiTree T) {
    if (T == NULL) {
        return 0;
    }
    
    int leftHeight = GetHeight(T->lchild);
    int rightHeight = GetHeight(T->rchild);
    
    return (leftHeight > rightHeight ? leftHeight : rightHeight) + 1;
}

// （2）在二叉树上查找指定的结点
BiTree SearchNode(BiTree T, int key) {
    if (T == NULL) {
        return NULL;
    }
    
    if (T->data == key) {
        return T;
    }
    
    // 在左子树中查找
    BiTree leftResult = SearchNode(T->lchild, key);
    if (leftResult != NULL) {
        return leftResult;
    }
    
    // 在右子树中查找
    return SearchNode(T->rchild, key);
}

// （3）统计二叉树中的节点个数
int CountNodes(BiTree T) {
    if (T == NULL) {
        return 0;
    }
    
    return CountNodes(T->lchild) + CountNodes(T->rchild) + 1;
}

// （4）先序遍历（递归）
void PreorderTraversal(BiTree T) {
    if (T != NULL) {
        printf("%d ", T->data);
        PreorderTraversal(T->lchild);
        PreorderTraversal(T->rchild);
    }
}

// 中序遍历（递归）
void InorderTraversal(BiTree T) {
    if (T != NULL) {
        InorderTraversal(T->lchild);
        printf("%d ", T->data);
        InorderTraversal(T->rchild);
    }
}

// 后序遍历（递归）
void PostorderTraversal(BiTree T) {
    if (T != NULL) {
        PostorderTraversal(T->lchild);
        PostorderTraversal(T->rchild);
        printf("%d ", T->data);
    }
}

// （5）层序遍历
void LevelOrderTraversal(BiTree T) {
    if (T == NULL) return;
    
    Queue Q;
    InitQueue(&Q);
    EnQueue(&Q, T);
    
    while (!QueueEmpty(&Q)) {
        BiTree current = DeQueue(&Q);
        printf("%d ", current->data);
        
        if (current->lchild != NULL) {
            EnQueue(&Q, current->lchild);
        }
        if (current->rchild != NULL) {
            EnQueue(&Q, current->rchild);
        }
    }
}

// （6）判断值为x和y的结点是否互为兄弟结点
bool AreSiblings(BiTree T, int x, int y) {
    if (T == NULL) {
        return false;
    }
    
    // 检查当前节点的左右子节点是否为x和y
    if (T->lchild != NULL && T->rchild != NULL) {
        if ((T->lchild->data == x && T->rchild->data == y) ||
            (T->lchild->data == y && T->rchild->data == x)) {
            return true;
        }
    }
    
    // 递归检查左右子树
    return AreSiblings(T->lchild, x, y) || AreSiblings(T->rchild, x, y);
}

// （7）判断是否为完全二叉树
bool IsCompleteBinaryTree(BiTree T) {
    if (T == NULL) return true;
    
    Queue Q;
    InitQueue(&Q);
    EnQueue(&Q, T);
    
    bool foundNull = false;  // 是否遇到了空节点
    
    while (!QueueEmpty(&Q)) {
        BiTree current = DeQueue(&Q);
        
        if (current == NULL) {
            foundNull = true;
        } else {
            // 如果之前遇到了空节点，现在又遇到非空节点，说明不是完全二叉树
            if (foundNull) {
                return false;
            }
            
            // 将左右子节点入队（包括NULL）
            EnQueue(&Q, current->lchild);
            EnQueue(&Q, current->rchild);
        }
    }
    
    return true;
}

// 辅助函数：查找节点的父节点
BiTree FindParent(BiTree T, int value) {
    if (T == NULL || (T->lchild == NULL && T->rchild == NULL)) {
        return NULL;
    }
    
    if ((T->lchild && T->lchild->data == value) ||
        (T->rchild && T->rchild->data == value)) {
        return T;
    }
    
    BiTree leftResult = FindParent(T->lchild, value);
    if (leftResult != NULL) {
        return leftResult;
    }
    
    return FindParent(T->rchild, value);
}

// 辅助函数：获取节点深度
int GetNodeDepth(BiTree T, int value, int depth) {
    if (T == NULL) {
        return -1;
    }
    
    if (T->data == value) {
        return depth;
    }
    
    int leftDepth = GetNodeDepth(T->lchild, value, depth + 1);
    if (leftDepth != -1) {
        return leftDepth;
    }
    
    return GetNodeDepth(T->rchild, value, depth + 1);
}

// 释放二叉树内存
void DestroyBinaryTree(BiTree T) {
    if (T != NULL) {
        DestroyBinaryTree(T->lchild);
        DestroyBinaryTree(T->rchild);
        free(T);
    }
}

// 打印二叉树结构（简单的可视化）
void PrintTreeStructure(BiTree T, int depth, char prefix) {
    if (T == NULL) return;
    
    for (int i = 0; i < depth; i++) {
        printf("  ");
    }
    printf("%c%d\n", prefix, T->data);
    
    if (T->lchild != NULL || T->rchild != NULL) {
        if (T->lchild != NULL) {
            PrintTreeStructure(T->lchild, depth + 1, 'L');
        } else {
            for (int i = 0; i <= depth; i++) printf("  ");
            printf("L(NULL)\n");
        }
        
        if (T->rchild != NULL) {
            PrintTreeStructure(T->rchild, depth + 1, 'R');
        } else {
            for (int i = 0; i <= depth; i++) printf("  ");
            printf("R(NULL)\n");
        }
    }
}

// ===== 主函数测试 =====
int main() {
    printf("=== 二叉树基本操作测试 ===\n\n");
    
    // 创建图1所示的二叉树
    BiTree root = CreateBinaryTree();
    
    printf("【二叉树结构】\n");
    printf("根据图1创建的二叉树结构如下：\n");
    PrintTreeStructure(root, 0, ' ');
    printf("\n");
    
    // （1）求二叉树的高度
    printf("【1. 求二叉树的高度】\n");
    int height = GetHeight(root);
    printf("二叉树的高度为: %d\n\n", height);
    
    // （2）查找指定节点
    printf("【2. 查找指定节点】\n");
    int searchValues[] = {3, 6, 7, 1};
    int numSearch = sizeof(searchValues) / sizeof(searchValues[0]);
    
    for (int i = 0; i < numSearch; i++) {
        BiTree found = SearchNode(root, searchValues[i]);
        if (found != NULL) {
            printf("找到节点 %d，地址: %p\n", searchValues[i], (void*)found);
        } else {
            printf("未找到节点 %d\n", searchValues[i]);
        }
    }
    printf("\n");
    
    // （3）统计节点个数
    printf("【3. 统计节点个数】\n");
    int nodeCount = CountNodes(root);
    printf("二叉树中的节点个数为: %d\n\n", nodeCount);
    
    // （4）三种递归遍历
    printf("【4. 递归遍历】\n");
    printf("先序遍历: ");
    PreorderTraversal(root);
    printf("\n");
    
    printf("中序遍历: ");
    InorderTraversal(root);
    printf("\n");
    
    printf("后序遍历: ");
    PostorderTraversal(root);
    printf("\n\n");
    
    // （5）层序遍历
    printf("【5. 层序遍历】\n");
    printf("层序遍历: ");
    LevelOrderTraversal(root);
    printf("\n\n");
    
    // （6）判断兄弟节点
    printf("【6. 判断兄弟节点】\n");
    int siblingPairs[][2] = {{2, 3}, {4, 5}, {1, 2}, {3, 6}, {2, 4}};
    int numPairs = sizeof(siblingPairs) / sizeof(siblingPairs[0]);
    
    for (int i = 0; i < numPairs; i++) {
        int x = siblingPairs[i][0];
        int y = siblingPairs[i][1];
        bool areSiblings = AreSiblings(root, x, y);
        printf("节点 %d 和节点 %d %s兄弟节点\n", 
               x, y, areSiblings ? "是" : "不是");
    }
    printf("\n");
    
    // （7）判断是否为完全二叉树
    printf("【7. 判断是否为完全二叉树】\n");
    bool isComplete = IsCompleteBinaryTree(root);
    printf("该二叉树%s完全二叉树\n", isComplete ? "是" : "不是");
    
    // 解释为什么不是完全二叉树
    if (!isComplete) {
        printf("解释：完全二叉树要求除最后一层外，其他层都是满的，\n");
        printf("且最后一层的节点都靠左排列。\n");
        printf("该树第3层（节点4,5）不是靠左连续排列，\n");
        printf("且第4层（节点6）的父节点4不是最左边的节点。\n");
    }
    printf("\n");
    
    // 额外信息展示
    printf("【额外信息】\n");
    printf("各节点的深度信息：\n");
    for (int i = 1; i <= 6; i++) {
        int depth = GetNodeDepth(root, i, 0);
        if (depth != -1) {
            printf("节点 %d 的深度: %d\n", i, depth);
        }
    }
    printf("\n");
    
    // 释放内存
    DestroyBinaryTree(root);
    printf("=== 测试完成，内存已释放 ===\n");
    
    return 0;
}