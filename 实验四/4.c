#include <stdio.h>
#include <stdlib.h>

#define MAX_SIZE 32  // �������Ǵ������32λ������

// ����ջ
typedef struct {
    int data[MAX_SIZE];
    int top;
} Stack;

// ��ʼ��ջ
void initStack(Stack* s) {
    s->top = -1;
}

// �ж�ջ�Ƿ�Ϊ��
int isEmpty(Stack* s) {
    return s->top == -1;
}

// �ж�ջ�Ƿ���
int isFull(Stack* s) {
    return s->top == MAX_SIZE - 1;
}

// ѹջ
void push(Stack* s, int value) {
    if (isFull(s)) {
        printf("ջ����\n");
        return;
    }
    s->data[++s->top] = value;
}

// ��ջ
int pop(Stack* s) {
    if (isEmpty(s)) {
        printf("ջ�գ�\n");
        return -1;
    }
    return s->data[s->top--];
}

// ʮ����ת������
void decimalToBinary(int num) {
    Stack s;
    initStack(&s);
    if (num == 0) {
        printf("������: 0\n");
        return;
    }

    while (num > 0) {
        push(&s, num % 2);  // ȡ����
        num = num / 2;      // ����
    }

    printf("������: ");
    while (!isEmpty(&s)) {
        printf("%d", pop(&s));  // ��˳��ջ
    }
    printf("\n");
}

// ����������
int main() {
    int num;
    printf("������һ��ʮ��������: ");
    scanf_s("%d", &num);
    decimalToBinary(num);
    return 0;
}
